using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Services;

public class EmailVerificationService : IEmailVerificationService
{
    private readonly IVerificationService _verificationService;
    private readonly AppDbContext _context;
    private readonly EmailHelper _emailHelper;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<EmailVerificationService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _environment;

    public EmailVerificationService(
        IVerificationService verificationService,
        AppDbContext context,
        EmailHelper emailHelper,
        IStringLocalizer<SharedResource> localizer,
        ILogger<EmailVerificationService> logger,
        IConfiguration configuration,
        IWebHostEnvironment environment)
    {
        _verificationService = verificationService;
        _context = context;
        _emailHelper = emailHelper;
        _localizer = localizer;
        _logger = logger;
        _configuration = configuration;
        _environment = environment;
    }

    public async Task<string> GenerateVerificationTokenAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null) throw new ArgumentException("User not found", nameof(userId));
        return await _verificationService.GenerateVerificationTokenAsync(userId, VerificationType.Email, user.Email);
    }

    public async Task<bool> SendVerificationEmailAsync(int userId, string email, string userName)
    {
        try
        {
            if (!await CanSendVerificationEmailAsync(userId))
            {
                _logger.LogWarning("Rate limit exceeded for user {UserId}", userId);
                return false;
            }

            var token = await GenerateVerificationTokenAsync(userId);
            var baseUrl = _configuration["BaseUrl"] ?? "https://localhost:5001";
            var verificationUrl = $"{baseUrl}/EmailVerification/Verify?token={token}";

            var subject = _localizer["Email Verification Required"];
            var body = PrepareEmailBody(userName, verificationUrl);

            var emailMessage = new EmailMessage
            {
                Subject = subject,
                Body = body,
                IsHtml = true,
                ToAddresses = new List<EmailAddress> { new EmailAddress(email) }
            };

            var emailSent = await _emailHelper.SendEmailAsync(emailMessage);
            await UpdateAttemptTrackingAsync(userId);

            if (emailSent)
            {
                _logger.LogInformation("Email verification sent successfully to user {UserId}", userId);
            }
            else
            {
                _logger.LogWarning("Failed to send email verification to user {UserId}", userId);
            }

            return emailSent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending verification email to user {UserId}", userId);
            return false;
        }
    }

    private async Task UpdateAttemptTrackingAsync(int userId)
    {
        if (_verificationService is VerificationService verificationService)
        {
            await verificationService.UpdateAttemptTrackingAsync(userId, VerificationType.Email);
        }
    }

    private string PrepareEmailBody(string userName, string verificationUrl)
    {
        // Use generic branding for non-production environments
        var siteName = _environment.IsProduction() ? "RazeWin" : "Test Platform";
        var currentYear = DateTime.Now.Year;

        return $@"<html><body>
<h1>{_localizer["Email Verification Required"]}</h1>
<p>{_localizer["Hello"]} {userName},</p>
<p>Thank you for registering with {siteName}. To complete your registration, please verify your email address by clicking the button below:</p>
<p><a href='{verificationUrl}' style='background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>{_localizer["Verify Email Address"]}</a></p>
<p>{_localizer["If the button doesn't work, you can copy and paste this link into your browser:"]}</p>
<p style='word-break: break-all;'>{verificationUrl}</p>
<p>{_localizer["This verification link will expire in 24 hours."]}</p>
<p>{_localizer["If you didn't create an account with us, please ignore this email."]}</p>
<p>&copy; {currentYear} {siteName}. {_localizer["All rights reserved."]}</p>
</body></html>";
    }

    public async Task<bool> VerifyEmailTokenAsync(string token)
    {
        return await _verificationService.VerifyTokenAsync(token);
    }

    public async Task<bool> CanSendVerificationEmailAsync(int userId)
    {
        return await _verificationService.CanSendVerificationAsync(userId, VerificationType.Email);
    }

    public async Task<TimeSpan?> GetTimeUntilNextEmailAsync(int userId)
    {
        return await _verificationService.GetTimeUntilNextVerificationAsync(userId, VerificationType.Email);
    }

    public async Task<int> GetTodayAttemptsAsync(int userId)
    {
        return await _verificationService.GetTodayAttemptsAsync(userId, VerificationType.Email);
    }

    public async Task<bool> IsEmailVerifiedAsync(int userId)
    {
        return await _verificationService.IsVerifiedAsync(userId, VerificationType.Email);
    }

    public async Task<EmailVerificationStatus> GetVerificationStatusAsync(int userId)
    {
        var status = await _verificationService.GetVerificationStatusAsync(userId, VerificationType.Email);
        return new EmailVerificationStatus
        {
            IsVerified = status.IsVerified,
            VerifiedDate = status.VerifiedDate,
            CanSendVerification = status.CanSendVerification,
            TimeUntilNextVerification = status.TimeUntilNextVerification,
            TodayAttempts = status.TodayAttempts,
            MaxDailyAttempts = status.MaxDailyAttempts,
            MinutesBetweenAttempts = status.MinutesBetweenAttempts,
            TargetValue = status.TargetValue
        };
    }
}

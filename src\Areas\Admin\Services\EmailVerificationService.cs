using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System.Security.Cryptography;
using System.Text;

namespace RazeWinComTr.Areas.Admin.Services;

public class EmailVerificationService : IEmailVerificationService
{
    private readonly AppDbContext _context;
    private readonly EmailHelper _emailHelper;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<EmailVerificationService> _logger;
    private readonly IConfiguration _configuration;

    // Rate limiting constants
    private const int MAX_DAILY_ATTEMPTS = 5;
    private const int MINUTES_BETWEEN_ATTEMPTS = 5;
    private const int TOKEN_EXPIRY_HOURS = 24;

    public EmailVerificationService(
        AppDbContext context,
        EmailHelper emailHelper,
        IStringLocalizer<SharedResource> localizer,
        ILogger<EmailVerificationService> logger,
        IConfiguration configuration)
    {
        _context = context;
        _emailHelper = emailHelper;
        _localizer = localizer;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<string> GenerateVerificationTokenAsync(int userId)
    {
        // Generate a secure random token
        var tokenBytes = new byte[32];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(tokenBytes);
        }
        
        var token = Convert.ToBase64String(tokenBytes)
            .Replace("+", "-")
            .Replace("/", "_")
            .Replace("=", "");

        // Update user with new token
        var user = await _context.Users.FindAsync(userId);
        if (user != null)
        {
            user.EmailVerificationToken = token;
            user.EmailVerificationTokenExpiry = DateTime.UtcNow.AddHours(TOKEN_EXPIRY_HOURS);
            await _context.SaveChangesAsync();
        }

        return token;
    }

    public async Task<bool> SendVerificationEmailAsync(int userId, string email, string userName)
    {
        try
        {
            // Check rate limiting
            if (!await CanSendVerificationEmailAsync(userId))
            {
                _logger.LogWarning("Rate limit exceeded for user {UserId}", userId);
                return false;
            }

            // Generate new token
            var token = await GenerateVerificationTokenAsync(userId);

            // Create verification URL
            var baseUrl = _configuration["BaseUrl"] ?? "https://localhost:5001";
            var verificationUrl = $"{baseUrl}/EmailVerification/Verify?token={token}";

            // Prepare email content
            var subject = _localizer["Email Verification Required"];
            var body = await PrepareEmailBodyAsync(userName, verificationUrl);

            // Send email
            var emailMessage = new EmailMessage
            {
                Subject = subject,
                Body = body,
                IsHtml = true,
                ToAddresses = new List<EmailAddress> { new EmailAddress(email) }
            };

            var emailSent = await _emailHelper.SendEmailAsync(emailMessage);

            // Update attempt tracking regardless of email success
            await UpdateAttemptTrackingAsync(userId);

            if (emailSent)
            {
                _logger.LogInformation("Email verification sent successfully to user {UserId}", userId);
            }
            else
            {
                _logger.LogWarning("Failed to send email verification to user {UserId}", userId);
            }

            return emailSent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending verification email to user {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> VerifyEmailTokenAsync(string token)
    {
        try
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.EmailVerificationToken == token);

            if (user == null)
            {
                _logger.LogWarning("Invalid verification token: {Token}", token);
                return false;
            }

            // Check if token is expired
            if (user.EmailVerificationTokenExpiry == null || 
                user.EmailVerificationTokenExpiry < DateTime.UtcNow)
            {
                _logger.LogWarning("Expired verification token for user {UserId}", user.UserId);
                return false;
            }

            // Mark email as verified
            user.EmailVerified = true;
            user.EmailVerifiedDate = DateTime.UtcNow;
            user.EmailVerificationToken = null;
            user.EmailVerificationTokenExpiry = null;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Email verified successfully for user {UserId}", user.UserId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying email token: {Token}", token);
            return false;
        }
    }

    public async Task<bool> CanSendVerificationEmailAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null) return false;

        // Check if email is already verified
        if (user.EmailVerified) return false;

        var now = DateTime.UtcNow;

        // Check daily limit
        if (user.LastEmailVerificationAttempt?.Date == now.Date)
        {
            if (user.EmailVerificationAttempts >= MAX_DAILY_ATTEMPTS)
            {
                return false;
            }

            // Check time between attempts
            var timeSinceLastAttempt = now - user.LastEmailVerificationAttempt.Value;
            if (timeSinceLastAttempt.TotalMinutes < MINUTES_BETWEEN_ATTEMPTS)
            {
                return false;
            }
        }

        return true;
    }

    public async Task<TimeSpan?> GetTimeUntilNextEmailAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null || user.EmailVerified) return null;

        var now = DateTime.UtcNow;

        // If no previous attempt or different day, can send now
        if (user.LastEmailVerificationAttempt == null || 
            user.LastEmailVerificationAttempt.Value.Date != now.Date)
        {
            return null;
        }

        // Check daily limit
        if (user.EmailVerificationAttempts >= MAX_DAILY_ATTEMPTS)
        {
            // Must wait until next day
            var nextDay = user.LastEmailVerificationAttempt.Value.Date.AddDays(1);
            return nextDay - now;
        }

        // Check time between attempts
        var nextAllowedTime = user.LastEmailVerificationAttempt.Value.AddMinutes(MINUTES_BETWEEN_ATTEMPTS);
        if (now < nextAllowedTime)
        {
            return nextAllowedTime - now;
        }

        return null;
    }

    public async Task<int> GetTodayAttemptsAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null) return 0;

        var today = DateTime.UtcNow.Date;
        if (user.LastEmailVerificationAttempt?.Date == today)
        {
            return user.EmailVerificationAttempts;
        }

        return 0;
    }

    public async Task<bool> IsEmailVerifiedAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        return user?.EmailVerified ?? false;
    }

    public async Task<EmailVerificationStatus> GetVerificationStatusAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
        {
            return new EmailVerificationStatus();
        }

        var canSend = await CanSendVerificationEmailAsync(userId);
        var timeUntilNext = await GetTimeUntilNextEmailAsync(userId);
        var todayAttempts = await GetTodayAttemptsAsync(userId);

        return new EmailVerificationStatus
        {
            IsVerified = user.EmailVerified,
            VerifiedDate = user.EmailVerifiedDate,
            CanSendEmail = canSend,
            TimeUntilNextEmail = timeUntilNext,
            TodayAttempts = todayAttempts,
            MaxDailyAttempts = MAX_DAILY_ATTEMPTS,
            MinutesBetweenAttempts = MINUTES_BETWEEN_ATTEMPTS
        };
    }

    private async Task UpdateAttemptTrackingAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null) return;

        var now = DateTime.UtcNow;

        // Reset counter if it's a new day
        if (user.LastEmailVerificationAttempt?.Date != now.Date)
        {
            user.EmailVerificationAttempts = 1;
        }
        else
        {
            user.EmailVerificationAttempts++;
        }

        user.LastEmailVerificationAttempt = now;
        await _context.SaveChangesAsync();
    }

    private Task<string> PrepareEmailBodyAsync(string userName, string verificationUrl)
    {
        // This is a basic HTML template. In a real application, you might want to use a proper template engine
        var body = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>{_localizer["Email Verification"]}</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f8f9fa; }}
        .button {{ display: inline-block; padding: 12px 24px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>{_localizer["Email Verification Required"]}</h1>
        </div>
        <div class='content'>
            <p>{_localizer["Hello"]} {userName},</p>
            <p>{_localizer["Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:"]}</p>
            <p style='text-align: center;'>
                <a href='{verificationUrl}' class='button'>{_localizer["Verify Email Address"]}</a>
            </p>
            <p>{_localizer["If the button doesn't work, you can copy and paste this link into your browser:"]}</p>
            <p style='word-break: break-all;'>{verificationUrl}</p>
            <p>{_localizer["This verification link will expire in 24 hours."]}</p>
            <p>{_localizer["If you didn't create an account with us, please ignore this email."]}</p>
        </div>
        <div class='footer'>
            <p>&copy; 2024 RazeWin. {_localizer["All rights reserved."]}</p>
        </div>
    </div>
</body>
</html>";

        return Task.FromResult(body);
    }
}

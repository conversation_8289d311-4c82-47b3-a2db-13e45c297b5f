using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Services;

public class EmailVerificationService : IEmailVerificationService
{
    private readonly IVerificationService _verificationService;
    private readonly AppDbContext _context;
    private readonly EmailHelper _emailHelper;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<EmailVerificationService> _logger;
    private readonly IConfiguration _configuration;

    public EmailVerificationService(
        IVerificationService verificationService,
        AppDbContext context,
        EmailHelper emailHelper,
        IStringLocalizer<SharedResource> localizer,
        ILogger<EmailVerificationService> logger,
        IConfiguration configuration)
    {
        _verificationService = verificationService;
        _context = context;
        _emailHelper = emailHelper;
        _localizer = localizer;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<string> GenerateVerificationTokenAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null) throw new ArgumentException("User not found", nameof(userId));
        return await _verificationService.GenerateVerificationTokenAsync(userId, VerificationType.Email, user.Email);
    }

    public async Task<bool> SendVerificationEmailAsync(int userId, string email, string userName)
    {
        return true; // Simplified for now
    }

    public async Task<bool> VerifyEmailTokenAsync(string token)
    {
        return await _verificationService.VerifyTokenAsync(token);
    }

    public async Task<bool> CanSendVerificationEmailAsync(int userId)
    {
        return await _verificationService.CanSendVerificationAsync(userId, VerificationType.Email);
    }

    public async Task<TimeSpan?> GetTimeUntilNextEmailAsync(int userId)
    {
        return await _verificationService.GetTimeUntilNextVerificationAsync(userId, VerificationType.Email);
    }

    public async Task<int> GetTodayAttemptsAsync(int userId)
    {
        return await _verificationService.GetTodayAttemptsAsync(userId, VerificationType.Email);
    }

    public async Task<bool> IsEmailVerifiedAsync(int userId)
    {
        return await _verificationService.IsVerifiedAsync(userId, VerificationType.Email);
    }

    public async Task<EmailVerificationStatus> GetVerificationStatusAsync(int userId)
    {
        var status = await _verificationService.GetVerificationStatusAsync(userId, VerificationType.Email);
        return new EmailVerificationStatus
        {
            IsVerified = status.IsVerified,
            VerifiedDate = status.VerifiedDate,
            CanSendVerification = status.CanSendVerification,
            TimeUntilNextVerification = status.TimeUntilNextVerification,
            TodayAttempts = status.TodayAttempts,
            MaxDailyAttempts = status.MaxDailyAttempts,
            MinutesBetweenAttempts = status.MinutesBetweenAttempts,
            TargetValue = status.TargetValue
        };
    }
}

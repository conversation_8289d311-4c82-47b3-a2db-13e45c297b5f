namespace RazeWinComTr.Areas.MyAccount.ViewModels;

/// <summary>
/// Email verification status and UI information for profile page
/// </summary>
public class EmailVerificationViewModel
{
    /// <summary>
    /// Whether the user's email is verified
    /// </summary>
    public bool IsEmailVerified { get; set; }

    /// <summary>
    /// Date when email was verified (if verified)
    /// </summary>
    public DateTime? EmailVerifiedDate { get; set; }

    /// <summary>
    /// Whether user can send a verification email now (not rate limited)
    /// </summary>
    public bool CanSendVerificationEmail { get; set; }

    /// <summary>
    /// Time remaining until user can send another verification email
    /// </summary>
    public TimeSpan? TimeUntilNextEmail { get; set; }

    /// <summary>
    /// Number of verification email attempts made today
    /// </summary>
    public int TodayEmailAttempts { get; set; }

    /// <summary>
    /// Maximum number of verification emails allowed per day
    /// </summary>
    public int MaxDailyEmailAttempts { get; set; } = 5;

    /// <summary>
    /// Minutes that must pass between verification email attempts
    /// </summary>
    public int MinutesBetweenAttempts { get; set; } = 5;

    /// <summary>
    /// User's email address
    /// </summary>
    public string EmailAddress { get; set; } = string.Empty;

    /// <summary>
    /// Gets a user-friendly status message for display
    /// </summary>
    public string GetStatusMessage()
    {
        if (IsEmailVerified)
        {
            return $"Verified on {EmailVerifiedDate:dd/MM/yyyy}";
        }

        if (!CanSendVerificationEmail)
        {
            if (TimeUntilNextEmail.HasValue)
            {
                var minutes = (int)Math.Ceiling(TimeUntilNextEmail.Value.TotalMinutes);
                return $"Next email in {minutes} minutes";
            }
            else
            {
                return $"Daily limit reached ({TodayEmailAttempts}/{MaxDailyEmailAttempts})";
            }
        }

        return "Not verified";
    }

    /// <summary>
    /// Gets CSS class for status display
    /// </summary>
    public string GetStatusCssClass()
    {
        return IsEmailVerified ? "text-success" : "text-warning";
    }

    /// <summary>
    /// Gets icon class for status display
    /// </summary>
    public string GetStatusIconClass()
    {
        return IsEmailVerified ? "fas fa-check-circle" : "fas fa-exclamation-triangle";
    }
}

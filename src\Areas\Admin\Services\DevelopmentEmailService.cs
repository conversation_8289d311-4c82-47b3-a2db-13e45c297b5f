using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Development email service that modifies email content for testing
/// </summary>
public class DevelopmentEmailService
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<DevelopmentEmailService> _logger;
    private readonly IWebHostEnvironment _environment;

    public DevelopmentEmailService(
        IStringLocalizer<SharedResource> localizer,
        ILogger<DevelopmentEmailService> logger,
        IWebHostEnvironment environment)
    {
        _localizer = localizer;
        _logger = logger;
        _environment = environment;
    }

    /// <summary>
    /// Modifies email content for development/testing environments
    /// </summary>
    /// <param name="emailMessage">Original email message</param>
    /// <param name="originalRecipient">Original recipient email</param>
    /// <returns>Modified email message for development</returns>
    public EmailMessage PrepareForDevelopment(EmailMessage emailMessage, string originalRecipient)
    {
        if (!_environment.IsDevelopment())
        {
            return emailMessage;
        }

        // Clone the email message
        var devEmail = new EmailMessage
        {
            Subject = PrepareDevSubject(emailMessage.Subject),
            Body = PrepareDevBody(emailMessage.Body, originalRecipient),
            IsHtml = emailMessage.IsHtml,
            ToAddresses = GetDevelopmentEmailAddresses()
        };

        _logger.LogInformation("Email modified for development environment. Original recipient: {OriginalRecipient}", originalRecipient);

        return devEmail;
    }

    /// <summary>
    /// Gets development email addresses (can be configured)
    /// </summary>
    private List<EmailAddress> GetDevelopmentEmailAddresses()
    {
        // You can configure these in appsettings.Development.json
        return new List<EmailAddress>
        {
            new EmailAddress("<EMAIL>"), // Replace with your temp email
            // Add more test emails if needed
        };
    }

    /// <summary>
    /// Prepares development email subject
    /// </summary>
    private string PrepareDevSubject(string originalSubject)
    {
        return $"[DEV-TEST] {originalSubject}";
    }

    /// <summary>
    /// Prepares development email body with test information
    /// </summary>
    private string PrepareDevBody(string originalBody, string originalRecipient)
    {
        var devNotice = $@"
<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin-bottom: 20px; border-radius: 5px;'>
    <h3 style='color: #856404; margin: 0 0 10px 0;'>🧪 DEVELOPMENT TEST EMAIL</h3>
    <p style='color: #856404; margin: 0;'>
        <strong>Original Recipient:</strong> {originalRecipient}<br>
        <strong>Environment:</strong> Development<br>
        <strong>Timestamp:</strong> {DateTime.Now:yyyy-MM-dd HH:mm:ss}<br>
        <strong>Note:</strong> This email was sent to a test address for development purposes.
    </p>
</div>";

        if (originalBody.Contains("<body>"))
        {
            // Insert after body tag for HTML emails
            return originalBody.Replace("<body>", $"<body>{devNotice}");
        }
        else if (originalBody.Contains("<html>"))
        {
            // Insert after html tag for HTML emails without body
            return originalBody.Replace("<html>", $"<html><body>{devNotice}") + "</body>";
        }
        else
        {
            // Plain text email
            var plainNotice = $@"
=====================================
🧪 DEVELOPMENT TEST EMAIL
=====================================
Original Recipient: {originalRecipient}
Environment: Development
Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
Note: This email was sent to a test address for development purposes.
=====================================

";
            return plainNotice + originalBody;
        }
    }

    /// <summary>
    /// Checks if current environment should use development email settings
    /// </summary>
    public bool ShouldUseDevelopmentEmail()
    {
        return _environment.IsDevelopment() || _environment.EnvironmentName == "Testing";
    }
}

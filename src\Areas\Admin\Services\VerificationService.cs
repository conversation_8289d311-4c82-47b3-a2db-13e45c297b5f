using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System.Security.Cryptography;

namespace RazeWinComTr.Areas.Admin.Services;

public class VerificationService : IVerificationService
{
    private readonly AppDbContext _context;
    private readonly ILogger<VerificationService> _logger;

    // Rate limiting constants
    private const int MAX_DAILY_ATTEMPTS = 5;
    private const int MINUTES_BETWEEN_ATTEMPTS = 5;
    private const int TOKEN_EXPIRY_HOURS = 24;

    public VerificationService(
        AppDbContext context,
        ILogger<VerificationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<string> GenerateVerificationTokenAsync(int userId, VerificationType verificationType, string targetValue)
    {
        // Generate a secure random token
        var tokenBytes = new byte[32];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(tokenBytes);
        }
        
        var token = Convert.ToBase64String(tokenBytes)
            .Replace("+", "-")
            .Replace("/", "_")
            .Replace("=", "");

        // Find existing verification record or create new one
        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.UserId == userId && 
                                    v.VerificationType == verificationType && 
                                    v.IsActive == 1);

        if (verification == null)
        {
            verification = new UserVerification
            {
                UserId = userId,
                VerificationType = verificationType,
                TargetValue = targetValue,
                CrDate = DateTime.UtcNow,
                IsActive = 1
            };
            _context.UserVerifications.Add(verification);
        }
        else
        {
            verification.TargetValue = targetValue;
            verification.ModDate = DateTime.UtcNow;
        }

        verification.VerificationToken = token;
        verification.TokenExpiry = DateTime.UtcNow.AddHours(TOKEN_EXPIRY_HOURS);

        await _context.SaveChangesAsync();
        return token;
    }

    public async Task<bool> VerifyTokenAsync(string token)
    {
        try
        {
            var verification = await _context.UserVerifications
                .FirstOrDefaultAsync(v => v.VerificationToken == token && v.IsActive == 1);

            if (verification == null)
            {
                _logger.LogWarning("Invalid verification token: {Token}", token);
                return false;
            }

            // Check if already verified
            if (verification.IsVerified && verification.VerifiedDate.HasValue)
            {
                _logger.LogInformation("Token already used for verification. User {UserId}, type {VerificationType}, verified on {VerifiedDate}",
                    verification.UserId, verification.VerificationType, verification.VerifiedDate);
                return true; // Return true to show "already verified" message
            }

            // Check if token is expired
            if (verification.TokenExpiry == null || verification.TokenExpiry < DateTime.UtcNow)
            {
                _logger.LogWarning("Expired verification token for user {UserId}, type {VerificationType}",
                    verification.UserId, verification.VerificationType);
                return false;
            }

            // Mark as verified
            verification.IsVerified = true;
            verification.VerifiedDate = DateTime.UtcNow;
            verification.ModDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Verification successful for user {UserId}, type {VerificationType}", 
                verification.UserId, verification.VerificationType);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying token: {Token}", token);
            return false;
        }
    }

    public async Task<bool> CanSendVerificationAsync(int userId, VerificationType verificationType)
    {
        var verification = await GetVerificationRecordAsync(userId, verificationType);
        if (verification == null) return true;

        // Check if already verified
        if (verification.IsVerified) return false;

        var now = DateTime.UtcNow;

        // Check daily limit
        if (verification.LastAttempt?.Date == now.Date)
        {
            if (verification.AttemptsCount >= MAX_DAILY_ATTEMPTS)
            {
                return false;
            }

            // Check time between attempts
            var timeSinceLastAttempt = now - verification.LastAttempt.Value;
            if (timeSinceLastAttempt.TotalMinutes < MINUTES_BETWEEN_ATTEMPTS)
            {
                return false;
            }
        }

        return true;
    }

    public async Task<TimeSpan?> GetTimeUntilNextVerificationAsync(int userId, VerificationType verificationType)
    {
        var verification = await GetVerificationRecordAsync(userId, verificationType);
        if (verification == null || verification.IsVerified) return null;

        var now = DateTime.UtcNow;

        // If no previous attempt or different day, can send now
        if (verification.LastAttempt == null || verification.LastAttempt.Value.Date != now.Date)
        {
            return null;
        }

        // Check daily limit
        if (verification.AttemptsCount >= MAX_DAILY_ATTEMPTS)
        {
            // Must wait until next day
            var nextDay = verification.LastAttempt.Value.Date.AddDays(1);
            return nextDay - now;
        }

        // Check time between attempts
        var nextAllowedTime = verification.LastAttempt.Value.AddMinutes(MINUTES_BETWEEN_ATTEMPTS);
        if (now < nextAllowedTime)
        {
            return nextAllowedTime - now;
        }

        return null;
    }

    public async Task<int> GetTodayAttemptsAsync(int userId, VerificationType verificationType)
    {
        var verification = await GetVerificationRecordAsync(userId, verificationType);
        if (verification == null) return 0;

        var today = DateTime.UtcNow.Date;
        if (verification.LastAttempt?.Date == today)
        {
            return verification.AttemptsCount;
        }

        return 0;
    }

    public async Task<bool> IsVerifiedAsync(int userId, VerificationType verificationType)
    {
        var verification = await GetVerificationRecordAsync(userId, verificationType);
        return verification?.IsVerified ?? false;
    }

    public async Task<VerificationStatus> GetVerificationStatusAsync(int userId, VerificationType verificationType)
    {
        var verification = await GetVerificationRecordAsync(userId, verificationType);
        
        var canSend = await CanSendVerificationAsync(userId, verificationType);
        var timeUntilNext = await GetTimeUntilNextVerificationAsync(userId, verificationType);
        var todayAttempts = await GetTodayAttemptsAsync(userId, verificationType);

        return new VerificationStatus
        {
            IsVerified = verification?.IsVerified ?? false,
            VerifiedDate = verification?.VerifiedDate,
            CanSendVerification = canSend,
            TimeUntilNextVerification = timeUntilNext,
            TodayAttempts = todayAttempts,
            MaxDailyAttempts = MAX_DAILY_ATTEMPTS,
            MinutesBetweenAttempts = MINUTES_BETWEEN_ATTEMPTS,
            TargetValue = verification?.TargetValue ?? string.Empty,
            VerificationType = verificationType
        };
    }

    public async Task UpdateAttemptTrackingAsync(int userId, VerificationType verificationType)
    {
        var verification = await GetVerificationRecordAsync(userId, verificationType);
        if (verification == null) return;

        var now = DateTime.UtcNow;

        // Reset counter if it's a new day
        if (verification.LastAttempt?.Date != now.Date)
        {
            verification.AttemptsCount = 1;
        }
        else
        {
            verification.AttemptsCount++;
        }

        verification.LastAttempt = now;
        verification.ModDate = now;
        await _context.SaveChangesAsync();
    }

    private async Task<UserVerification?> GetVerificationRecordAsync(int userId, VerificationType verificationType)
    {
        return await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.UserId == userId &&
                                    v.VerificationType == verificationType &&
                                    v.IsActive == 1);
    }

    public async Task<bool> IsTokenAlreadyVerifiedAsync(string token)
    {
        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.VerificationToken == token && v.IsActive == 1);

        return verification != null && verification.IsVerified && verification.VerifiedDate.HasValue;
    }
}

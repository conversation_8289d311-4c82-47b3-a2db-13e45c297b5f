using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Data;

public static class DbInitializer
{
    private static readonly Random _random = new();

    /// <summary>
    /// Creates a sample user with referral code and referrer
    /// </summary>
    private static User CreateSampleUser(int referrerId, string referralCode, DateTime now, int level, int index)
    {
        return new User
        {
            IsActive = 1,
            IdentityNumber = $"{_random.Next(10000000, 99999999)}{_random.Next(100, 999)}",
            PhoneNumber = $"05{_random.Next(10, 99)}{_random.Next(1000000, 9999999)}",
            Name = $"Level{level}",
            Surname = $"User{index}",
            Email = $"level{level}_user{index}_{Guid.NewGuid().ToString()[..6]}@example.com",
            PasswordHash = HashHelper.getHash("123123"),
            CrDate = now.AddDays(-_random.Next(1, 30)),
            Balance = _random.Next(10000, 100000),
            ReferrerId = referrerId,
            ReferralCode = referralCode,
            UserRoleRelations = [new UserRoleRelation { IsActive = 1, RoleId = (int)Roller.User }]
        };
    }

    /// <summary>
    /// Creates a hierarchical referral structure for a user
    /// </summary>
    private static async Task CreateReferralHierarchyAsync(AppDbContext context, ReferralService referralService, User parentUser, int level, int maxLevel)
    {
        if (level > maxLevel) return;

        // Number of users to create at this level
        int userCount = _random.Next(1, 5);
        var users = new List<User>();
        var now = DateTime.UtcNow;

        for (int i = 0; i < userCount; i++)
        {
            // Generate a unique referral code for the new user
            string referralCode = await referralService.GenerateUniqueReferralCodeAsync();

            // Create a new user with the parent as referrer
            var user = CreateSampleUser(parentUser.UserId, referralCode, now, level, i + 1);
            users.Add(user);
        }

        // Add users to database
        context.Users.AddRange(users);
        await context.SaveChangesAsync();

        // Create next level referrals for some of these users
        if (level < maxLevel)
        {
            // Select some users to have referrals (not all users will have referrals)
            var selectedUsers = users.OrderBy(u => Guid.NewGuid()).Take(Math.Max(1, users.Count / 2)).ToList();

            foreach (var user in selectedUsers)
            {
                await CreateReferralHierarchyAsync(context, referralService, user, level + 1, maxLevel);
            }
        }
    }

    public static async Task InitializeAsync(AppDbContext context, ReferralService referralService, SampleDataService sampleDataService, ITokenPriceService tokenPriceService, ReferralRewardService referralRewardService, string? fileStoragePath)
    {
        await context.Database.EnsureCreatedAsync();

        // Initialize RZW Savings Plans
        await InitializeRzwSavingsPlansAsync(context);

        // Update PackagePurchase transaction types
        await UpdatePackagePurchaseTransactionTypesAsync(context);

        // Migrate to TL and RZW reward system
        await MigrateToTlAndRzwRewardSystemAsync(context, tokenPriceService, referralRewardService);

        if (await context.Users.AnyAsync(p => p.ReferralCode == null || p.ReferralCode == ""))
        {
            var users = await context.Users.Where(p => p.ReferralCode == null || p.ReferralCode == "").ToListAsync();
            foreach (var user in users)
            {
                user.ReferralCode = await referralService.GenerateUniqueReferralCodeAsync();
                context.Users.Update(user);
            }
            await context.SaveChangesAsync();
        }
        var now = DateTime.UtcNow;
        if (!await context.Banks.AnyAsync())
        {
            context.Banks.Add(new Bank
            {
                AccountHolder = "Razewin Teknoloji Anonim Şirketi",
                BankName = "Akbank T.A.Ş.",
                Iban = "TR61 0004 6006 7488 8000 0845 55",
                CreatedDate = now,
                IsActive = true,
                Order = 1,
                Id = 0,
                ModifiedDate = null
            });
            context.Banks.Add(new Bank
            {
                AccountHolder = "Razewin Teknoloji Anonim Şirketi",
                BankName = "QNB Finansbank A.Ş.",
                Iban = "TR23 0011 1000 0000 0154 0472 49",
                CreatedDate = now,
                IsActive = true,
                Order = 2,
                Id = 0,
                ModifiedDate = null
            });
            context.Banks.Add(new Bank
            {
                AccountHolder = "Razewin Teknoloji Anonim Şirketi",
                BankName = "Papara Elektronik Para A.Ş.",
                Iban = "**********",
                CreatedDate = now,
                IsActive = true,
                Order = 3,
                Id = 0,
                ModifiedDate = null
            });

            await context.SaveChangesAsync();
        }

        if (!await context.Roles.AnyAsync())
        {
            context.Roles.Add(new Role
            {
                RoleId = (int)Roller.Admin,
                RoleName = Roller.Admin.ToString()
            });
            context.Roles.Add(new Role
            {
                RoleId = (int)Roller.User,
                RoleName = Roller.User.ToString()
            });
            await context.SaveChangesAsync();
        }

        if (!await context.Users.AnyAsync())
        {
            var users = new List<User>
            {
                new()
                {
                    IsActive = 1,
                    IdentityNumber = "1000",
                    PhoneNumber = "1234567890",
                    Name = "Admin",
                    Surname = "Admin",
                    Email = "<EMAIL>",
                    PasswordHash = HashHelper.getHash("Rzw2025**"),
                    CrDate = now,
                    UserRoleRelations= [new UserRoleRelation { IsActive = 1, RoleId = (int)Roller.Admin }],
                    ReferralCode = await referralService.GenerateUniqueReferralCodeAsync(),

                },
                new()
                {
                    IsActive = 1,
                    IdentityNumber = "1001",
                    PhoneNumber = "1234567890",
                    Name = "Developer",
                    Surname = "Developer",
                    Email = "<EMAIL>",
                    PasswordHash = HashHelper.getHash("Deve2025**"),
                    CrDate = now,
                    UserRoleRelations= [new UserRoleRelation { IsActive = 1, RoleId = (int)Roller.Admin }],
                    ReferralCode = await referralService.GenerateUniqueReferralCodeAsync(),

                },
                new()
                {
                    IsActive = 1,
                    IdentityNumber = "12332145665",
                    PhoneNumber = "05001112233",
                    Name = "Ali",
                    Surname = "Veli",
                    Email = "<EMAIL>",
                    PasswordHash = HashHelper.getHash("123123"),
                    CrDate = now,
                    Balance = 1_000_000_000,
                    ReferralCode = await referralService.GenerateUniqueReferralCodeAsync(),
                    UserRoleRelations= [new UserRoleRelation { IsActive = 1, RoleId = (int)Roller.User }]
                }
            };
            context.Users.AddRange(users);
            await context.SaveChangesAsync();
            await TempUpdateReferenceCode(context, referralService, sampleDataService);
            await TempCreateReferenceHierarchyForAliUser(context, referralService, sampleDataService);




            //// Assign roles to users
            //foreach (var user in users)
            //    context.UserRoleRelations.Add(new UserRoleRelation
            //    {
            //        UserId = user.UserId, // Assuming ID is the primary key in KULLANICI
            //        RoleId = (int)Roller.Admin,
            //        IsActive = 1
            //    });
            //await context.SaveChangesAsync();
        }

        if (!await context.Markets.AnyAsync())
        {
            var markets = new List<Market>
{
    new() { Id = 11, Coin = "RZW",  ShortName = "RZW",  Name = "Razewin",      PairCode = "RZWTRY" , IsApi = 0, ApiServiceName = null,                      BuyPrice = 0.5M,             SellPrice = 0.49M,              Change24h = 150M,   GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 0.00100000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 0.00100000M,  MaximumSell = decimal.MaxValue,     IconUrl = "markets/rzw.png",  IsActive = 1, Order = 0,  CrDate = now, ModDate = now },
    new() { Id = 1,  Coin = "BTC",  ShortName = "BTC",  Name = "Bitcoin",      PairCode = "BTCTRY" , IsApi = 1, ApiServiceName = ApiServiceNames.Bitexen,   BuyPrice = 35000.50000000M,  SellPrice = 35200.75000000M,    Change24h = 1.50M,  GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 0.00100000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 0.00100000M,  MaximumSell = decimal.MaxValue,     IconUrl = "markets/btc.png",  IsActive = 1, Order = 1,  CrDate = now, ModDate = now },
    new() { Id = 2,  Coin = "ETH",  ShortName = "ETH",  Name = "Ethereum",     PairCode = "ETHTRY" , IsApi = 1, ApiServiceName = ApiServiceNames.Bitexen,   BuyPrice = 1800.40000000M,   SellPrice = 1820.60000000M,     Change24h = -0.80M, GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 0.01000000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 0.01000000M,  MaximumSell = decimal.MaxValue,    IconUrl = "markets/eth.png",  IsActive = 1, Order = 2,  CrDate = now, ModDate = now },
    new() { Id = 3,  Coin = "XRP",  ShortName = "XRP",  Name = "Ripple",       PairCode = "XRPTRY" , IsApi = 1, ApiServiceName = ApiServiceNames.Bitexen,   BuyPrice = 0.50000000M,      SellPrice = 0.51000000M,        Change24h = 2.00M,  GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 10.00000000M, MaximumBuy = decimal.MaxValue, MinimumSell = 10.00000000M, MaximumSell = decimal.MaxValue, IconUrl = "markets/xrp.png",  IsActive = 1, Order = 3,  CrDate = now, ModDate = now },
    new() { Id = 4,  Coin = "DOGE", ShortName = "DOGE", Name = "Dogecoin",     PairCode = "DOGETRY", IsApi = 1, ApiServiceName = ApiServiceNames.Bitexen,   BuyPrice = 0.07000000M,      SellPrice = 0.07200000M,        Change24h = -1.20M, GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 10.00000000M, MaximumBuy = decimal.MaxValue, MinimumSell = 10.00000000M, MaximumSell = decimal.MaxValue, IconUrl = "markets/doge.png", IsActive = 1, Order = 4,  CrDate = now, ModDate = now },
    new() { Id = 5,  Coin = "ADA",  ShortName = "ADA",  Name = "Cardano",      PairCode = "ADATRY" , IsApi = 1, ApiServiceName = ApiServiceNames.Bitexen,   BuyPrice = 0.30000000M,      SellPrice = 0.32000000M,        Change24h = 0.50M,  GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 1.00000000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 1.00000000M,  MaximumSell = decimal.MaxValue, IconUrl = "markets/ada.png",  IsActive = 1, Order = 5,  CrDate = now, ModDate = now },
    new() { Id = 6,  Coin = "LTC",  ShortName = "LTC",  Name = "Litecoin",     PairCode = "LTCTRY" , IsApi = 1, ApiServiceName = ApiServiceNames.BTCTurk,   BuyPrice = 150.25000000M,    SellPrice = 152.10000000M,      Change24h = -0.70M, GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 0.10000000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 0.10000000M,  MaximumSell = decimal.MaxValue,   IconUrl = "markets/ltc.png",  IsActive = 0, Order = 6,  CrDate = now, ModDate = now },
    new() { Id = 7,  Coin = "DOT",  ShortName = "DOT",  Name = "Polkadot",     PairCode = "DOTTRY" , IsApi = 1, ApiServiceName = ApiServiceNames.Bitexen,   BuyPrice = 5.75000000M,      SellPrice = 5.90000000M,        Change24h = 3.20M,  GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 0.01000000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 0.01000000M,  MaximumSell = decimal.MaxValue,   IconUrl = "markets/dot.png",  IsActive = 1, Order = 7,  CrDate = now, ModDate = now },
    new() { Id = 8,  Coin = "SOL",  ShortName = "SOL",  Name = "Solana",       PairCode = "SOLTRY" , IsApi = 1, ApiServiceName = ApiServiceNames.Bitexen,   BuyPrice = 20.50000000M,     SellPrice = 21.00000000M,       Change24h = -2.50M, GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 0.01000000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 0.01000000M,  MaximumSell = decimal.MaxValue,    IconUrl = "markets/sol.png",  IsActive = 1, Order = 8,  CrDate = now, ModDate = now },
    new() { Id = 9,  Coin = "BNB",  ShortName = "BNB",  Name = "Binance Coin", PairCode = "BNBTRY" , IsApi = 1, ApiServiceName = ApiServiceNames.Bitexen,   BuyPrice = 250.00000000M,    SellPrice = 255.00000000M,      Change24h = 1.10M,  GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 0.01000000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 0.01000000M,  MaximumSell = decimal.MaxValue,     IconUrl = "markets/bnb.png",  IsActive = 1, Order = 9,  CrDate = now, ModDate = now },
    new() { Id = 10, Coin = "AVAX", ShortName = "AVAX", Name = "Avalanche",    PairCode = "AVAXTRY", IsApi = 1, ApiServiceName = ApiServiceNames.BTCTurk,   BuyPrice = 12.00000000M,     SellPrice = 12.50000000M,       Change24h = 0.30M,  GeneralIncrease = 0M, DecimalPlaces = 8, MinimumBuy = 0.01000000M,  MaximumBuy = decimal.MaxValue, MinimumSell = 0.01000000M,  MaximumSell = decimal.MaxValue,   IconUrl = "markets/avax.png", IsActive = 1, Order = 10, CrDate = now, ModDate = now }
};
            // Ensure fileStoragePath is not null before using it
            if (string.IsNullOrEmpty(fileStoragePath))
            {
                // Log a warning or handle the case where fileStoragePath is null
                Console.WriteLine("Warning: fileStoragePath is null or empty. Market icons will not be copied.");
                context.Markets.AddRange(markets);
                await context.SaveChangesAsync();
                return;
            }

            var marketIconsDirectory = Path.Combine(fileStoragePath, "markets");
            var sourcePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "site", "images", "markets");

            if (!Directory.Exists(marketIconsDirectory))
            {
                Directory.CreateDirectory(marketIconsDirectory);
            }
            foreach (var market in markets)
            {
                var sourceFile = Path.Combine(sourcePath, $"{market.Coin.ToLower()}.png");
                var destinationFile = Path.Combine(marketIconsDirectory, $"{market.Coin.ToLower()}.png");

                if (File.Exists(sourceFile) && !File.Exists(destinationFile))
                {
                    File.Copy(sourceFile, destinationFile);
                }
            }
            context.Markets.AddRange(markets);
            await context.SaveChangesAsync();
        }

        // Initialize referral packages if they don't exist
        if (!await context.Packages.AnyAsync())
        {
            var packages = new List<Package>
            {
                new()
                {
                    Id = 1,
                    Name = "Bronze",
                    Price = 5000,
                    Description = "Bronze referral package with basic benefits",
                    Benefits = "{\"Features\":[\"Basic Support\",\"5% Direct Referral Rewards\",\"2% Indirect Referral Rewards\",\"Referral code activation\"]}",
                    InviteLimit = 100,
                    EarningsCap = null,
                    IsActive = true,
                    Order = 1,
                    CreatedDate = now
                },
                new()
                {
                    Id = 2,
                    Name = "Silver",
                    Price = 10000,
                    Description = "Silver referral package with enhanced benefits",
                    Benefits = "{\"Features\":[\"Priority Support\",\"10% Direct Referral Rewards\",\"3% Indirect Referral Rewards\",\"Referral code activation\",\"Weekly Reports\"]}",
                    InviteLimit = 200,
                    EarningsCap = null,
                    IsActive = true,
                    Order = 2,
                    CreatedDate = now
                },
                new()
                {
                    Id = 3,
                    Name = "Gold",
                    Price = 15000,
                    Description = "Gold referral package with premium benefits",
                    Benefits = "{\"Features\":[\"VIP Support\",\"15% Direct Referral Rewards\",\"4% Indirect Referral Rewards\",\"Referral code activation\",\"Daily Reports\",\"Strategy Consultation\"]}",
                    InviteLimit = 400,
                    EarningsCap = null,
                    IsActive = true,
                    Order = 3,
                    CreatedDate = now
                },
                new()
                {
                    Id = 4,
                    Name = "Platinum",
                    Price = 20000,
                    Description = "Platinum referral package with exclusive benefits",
                    Benefits = "{\"Features\":[\"24/7 Dedicated Support\",\"20% Direct Referral Rewards\",\"5% Indirect Referral Rewards\",\"Referral code activation\",\"Real-time Reports\",\"Personal Account Manager\",\"Exclusive Events\"]}",
                    InviteLimit = null,
                    EarningsCap = null,
                    IsActive = true,
                    Order = 4,
                    CreatedDate = now
                }
            };

            context.Packages.AddRange(packages);
            await context.SaveChangesAsync();
        }

        // Initialize package reward percentages if they don't exist
        if (!await context.PackageRewardPercentages.AnyAsync())
        {
            var percentages = new List<PackageRewardPercentage>();
            var packages = await context.Packages.ToListAsync();

            // Find packages by name
            var bronzePackage = packages.FirstOrDefault(p => p.Name.Contains("bronze", StringComparison.OrdinalIgnoreCase));
            var silverPackage = packages.FirstOrDefault(p => p.Name.Contains("silver", StringComparison.OrdinalIgnoreCase));
            var goldPackage = packages.FirstOrDefault(p => p.Name.Contains("gold", StringComparison.OrdinalIgnoreCase));
            var platinumPackage = packages.FirstOrDefault(p => p.Name.Contains("platinum", StringComparison.OrdinalIgnoreCase));

            // Bronze package: 5% (1 level only)
            if (bronzePackage != null)
            {
                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = bronzePackage.Id,
                    Level = 1,
                    RzwPercentage = 5.0m,  // Level 1: 5%
                    CreatedDate = now
                });
            }

            // Silver package: 10%, 5% (2 levels)
            if (silverPackage != null)
            {
                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = silverPackage.Id,
                    Level = 1,
                    RzwPercentage = 10.0m,  // Level 1: 10%
                    CreatedDate = now
                });

                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = silverPackage.Id,
                    Level = 2,
                    RzwPercentage = 5.0m,   // Level 2: 5%
                    CreatedDate = now
                });
            }

            // Gold package: 15%, 10%, 5% (3 levels)
            if (goldPackage != null)
            {
                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = goldPackage.Id,
                    Level = 1,
                    RzwPercentage = 15.0m,  // Level 1: 15%
                    CreatedDate = now
                });

                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = goldPackage.Id,
                    Level = 2,
                    RzwPercentage = 10.0m,  // Level 2: 10%
                    CreatedDate = now
                });

                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = goldPackage.Id,
                    Level = 3,
                    RzwPercentage = 5.0m,   // Level 3: 5%
                    CreatedDate = now
                });
            }

            // Platinum package: 20%, 15%, 10%, 5% (4 levels)
            if (platinumPackage != null)
            {
                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = platinumPackage.Id,
                    Level = 1,
                    RzwPercentage = 20.0m,  // Level 1: 20%
                    CreatedDate = now
                });

                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = platinumPackage.Id,
                    Level = 2,
                    RzwPercentage = 15.0m,  // Level 2: 15%
                    CreatedDate = now
                });

                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = platinumPackage.Id,
                    Level = 3,
                    RzwPercentage = 10.0m,  // Level 3: 10%
                    CreatedDate = now
                });

                percentages.Add(new PackageRewardPercentage
                {
                    PackageId = platinumPackage.Id,
                    Level = 4,
                    RzwPercentage = 5.0m,   // Level 4: 5%
                    CreatedDate = now
                });
            }

            context.PackageRewardPercentages.AddRange(percentages);
            await context.SaveChangesAsync();
        }
        bool anySettingsRecordsExists = await context.Settings.AnyAsync();
        if (!anySettingsRecordsExists)
        {
            var settingDate = DateTime.Parse("2025-04-09 11:35:04");
            var settings = new List<Setting>
            {
                new() { Id = 1, Key = "site_title", Value = "RAZEWİN HOLDİNG", Description = "Site Başlığı", Group = "general", Order = 1, CrDate = settingDate },
                new() { Id = 2, Key = "site_description", Value = "RAZEWIN - Kripto Para Alım Satım Platformu", Description = "Site Açıklaması", Group = "general", Order = 2, CrDate = settingDate },
                new() { Id = 3, Key = "site_keywords", Value = "kripto,bitcoin,ethereum,altcoin,trading", Description = "Site Anahtar Kelimeleri", Group = "general", Order = 3, CrDate = settingDate },
                new() { Id = 4, Key = "site_email", Value = "<EMAIL>", Description = "Site Email Adresi", Group = "general", Order = 4, CrDate = settingDate },
                new() { Id = 5, Key = "site_phone", Value = "+90 507 707 72 93", Description = "Site Telefon", Group = "general", Order = 5, CrDate = settingDate },
                new() { Id = 6, Key = "site_address", Value = "İstanbul, Türkiye", Description = "Site Adres", Group = "general", Order = 6, CrDate = settingDate },
                new() { Id = 7, Key = "facebook_url", Value = null, Description = "Facebook URL", Group = "social", Order = 10, CrDate = settingDate },
                new() { Id = 8, Key = "twitter_url", Value = "https://x.com/razewintoken", Description = "Twitter URL", Group = "social", Order = 11, CrDate = settingDate },
                new() { Id = 9, Key = "instagram_url", Value = "https://instagram.com/razewintokenofficial", Description = "Instagram URL", Group = "social", Order = 12, CrDate = settingDate },
                new() { Id = 10, Key = "telegram_url", Value = "https://t.me/razewin_token", Description = "Telegram URL", Group = "social", Order = 13, CrDate = settingDate },

                new() { Id = 13, Key = "default_currency", Value = "TRY", Description = "Varsayılan Para Birimi", Group = "currency", Order = 30, CrDate = settingDate },
                new() { Id = 16, Key = "withdraw_commission", Value = "1.5", Description = "Çekim Komisyonu (%)", Group = "currency", Order = 33, CrDate = settingDate },

                new() { Id = 19, Key = "maintenance_mode", Value = "0", Description = "Bakım Modu (0: Kapalı, 1: Açık)", Group = "security", Order = 42, CrDate = settingDate },
#if DEBUG
                new() { Id = 20, Key = "smtp_host", Value = "smtp.mailgun.org", Description = "SMTP Sunucu", Group = "smtp", Order = 50, CrDate = settingDate },
                new() { Id = 21, Key = "smtp_port", Value = "587", Description = "SMTP Port", Group = "smtp", Order = 51, CrDate = settingDate },
                new() { Id = 22, Key = "smtp_user", Value = "<EMAIL>", Description = "SMTP Kullanıcı", Group = "smtp", Order = 52, CrDate = settingDate },
                new() { Id = 23, Key = "smtp_pass", Value = "Nr20252025**", Description = "SMTP Şifre", Group = "smtp", Order = 53, CrDate = settingDate },
                new() { Id = 24, Key = "smtp_crypto", Value = "tls", Description = "SMTP Güvenlik (tls/ssl)", Group = "smtp", Order = 54, CrDate = settingDate },
#else
                new() { Id = 20, Key = "smtp_host", Value = "razewin.com.tr", Description = "SMTP Sunucu", Group = "smtp", Order = 50, CrDate = settingDate },
                new() { Id = 21, Key = "smtp_port", Value = "465", Description = "SMTP Port", Group = "smtp", Order = 51, CrDate = settingDate },
                new() { Id = 22, Key = "smtp_user", Value = "<EMAIL>", Description = "SMTP Kullanıcı", Group = "smtp", Order = 52, CrDate = settingDate },
                new() { Id = 23, Key = "smtp_pass", Value = "8w6*B2j2e", Description = "SMTP Şifre", Group = "smtp", Order = 53, CrDate = settingDate },
                new() { Id = 24, Key = "smtp_crypto", Value = "tls", Description = "SMTP Güvenlik (tls/ssl)", Group = "smtp", Order = 54, CrDate = settingDate },
#endif
                new() { Id = 25, Key = "site_theme", Value = "default", Description = "Site Teması", Group = "theme", Order = 60, CrDate = settingDate },
                new() { Id = 26, Key = "admin_theme", Value = "default", Description = "Admin Panel Teması", Group = "theme", Order = 61, CrDate = settingDate },
                new() { Id = 29, Key = "favicon", Value = "/site/images/favicon.png", Description = "Favicon", Group = "theme", Order = 64, CrDate = settingDate },
                new() { Id = 30, Key = "logo_path", Value = "/public/image/GbErZcuBDF.png", Description = "Logo Path", Group = "theme", Order = 65, CrDate = settingDate },
                new() { Id = 31, Key = "whatsapp_number", Value = "+90 507 707 72 93", Description = "WhatsApp Numarası", Group = "contact", Order = 10, CrDate = settingDate },
                new() { Id = 32, Key = "javascript", Value = "var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();\n(function () {\n    var s1 = document.createElement(\"script\"), s0 = document.getElementsByTagName(\"script\")[0];\n    s1.async = true;\n    s1.src = '/embed.tawk.to/5eb2e7e6a1bad90e54a24950/default.js';\n    s1.charset = 'UTF-8';\n    s1.setAttribute('crossorigin', '*');\n    s0.parentNode.insertBefore(s1, s0);\n})();", Description = "Javascript Kodları (TAWK vb.)", Group = "code", Order = 20, CrDate = settingDate },
                new() { Id = 33, Key = "og_type", Value = "product", Description = "Open Graph Type", Group = "meta", Order = 130, CrDate = settingDate },
                new() { Id = 34, Key = "og_locale", Value = "tr_TR", Description = "Open Graph Locale", Group = "meta", Order = 131, CrDate = settingDate },
                new() { Id = 35, Key = "og_image", Value = "/i.hizliresim.com/89y4dw0.html", Description = "Open Graph Image", Group = "meta", Order = 132, CrDate = settingDate },
                new() { Id = 36, Key = "og_url", Value = "/index", Description = "Open Graph URL", Group = "meta", Order = 133, CrDate = settingDate },
                new() { Id = 37, Key = "twitter_card", Value = "summary", Description = "Twitter Card", Group = "meta", Order = 140, CrDate = settingDate },
                new() { Id = 38, Key = "twitter_url", Value = "/index", Description = "Twitter URL", Group = "meta", Order = 141, CrDate = settingDate },
                new() { Id = 39, Key = "twitter_image", Value = "/public/image/GbErZcuBDF.png", Description = "Twitter Image", Group = "meta", Order = 142, CrDate = settingDate },
                new() { Id = 40, Key = "twitter_site", Value = "/index", Description = "Twitter Site", Group = "meta", Order = 143, CrDate = settingDate },
                new() { Id = 41, Key = "meta_author", Value = "", Description = "Meta Author", Group = "meta", Order = 150, CrDate = settingDate },
                new() { Id = 42, Key = "meta_robots", Value = "All", Description = "Meta Robots", Group = "meta", Order = 151, CrDate = settingDate },
                new() { Id = 43, Key = "meta_googlebot", Value = "Index, Follow", Description = "Meta Googlebot", Group = "meta", Order = 152, CrDate = settingDate },
                new() { Id = 44, Key = "meta_rating", Value = "All", Description = "Meta Rating", Group = "meta", Order = 153, CrDate = settingDate },
                new() { Id = 45, Key = "meta_image", Value = "/index", Description = "Meta Image", Group = "meta", Order = 154, CrDate = settingDate },
                new() { Id = 46, Key = "papara_durum", Value = "1", Description = "Papara Aktivasyon Durumu", Group = "deposit", Order = 70, CrDate = settingDate },
                new() { Id = 48, Key = "papara_name", Value = "Razewin Teknoloji Anonim Şirketi", Description = "Papara Hesap Adı", Group = "deposit", Order = 71, CrDate = settingDate },
                new() { Id = 49, Key = "papara_number", Value = "**********", Description = "Papara Numarası", Group = "deposit", Order = 72, CrDate = settingDate },
                new() { Id = 50, Key = "papara_note", Value = "Lütfen açıklama kısmına kullanıcı ID'nizi yazınız", Description = "Papara Not", Group = "deposit", Order = 73, CrDate = settingDate },
                new() { Id = 57, Key = "btc_wallet", Value = "******************************************", Description = "Bitcoin Cüzdan Adresi", Group = "deposit", Order = 91, CrDate = settingDate },
            };

            context.Settings.AddRange(settings);
            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Updates the transaction type from "PackagePurchase" to "Package Purchase" in BalanceTransaction table
    /// </summary>
    private static async Task UpdatePackagePurchaseTransactionTypesAsync(AppDbContext context)
    {
        var transactionsToUpdate = await context.BalanceTransactions
            .Where(t => t.TransactionType.ToString() == "PackagePurchase")
            .ToListAsync();

        if (transactionsToUpdate.Any())
        {
            foreach (var transaction in transactionsToUpdate)
            {
                transaction.TransactionType = TransactionType.PackagePurchase;
            }
            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Migrates the existing reward system to the new TL and RZW reward system
    /// </summary>
    private static async Task MigrateToTlAndRzwRewardSystemAsync(AppDbContext context, ITokenPriceService tokenPriceService, ReferralRewardService referralRewardService)
    {
        // Check if we need to update existing records
        var anyPackageRewardPercentages = await context.PackageRewardPercentages.AnyAsync();
        var anyReferralRewards = await context.ReferralRewards.AnyAsync();

        if (!anyPackageRewardPercentages && !anyReferralRewards)
        {
            // No existing records to migrate
            return;
        }
        if (DateTime.Now.ToLocalTime() < new DateTime(2025, 5, 30) && context.PackageRewardPercentages.Any(p => p.TlPercentage == 0))
        {

            await UndoRewarDistributionByPercentage(context, tokenPriceService);

            // Get all package reward percentages
            var percentages = await context.PackageRewardPercentages.ToListAsync();

            foreach (var percentage in percentages)//update percentage values for new TL + RZW rewards distrubition
            {
                if (percentage.TlPercentage == 0)
                {
                    var tempOldFullPercentage = percentage.RzwPercentage;
                    // Set TL percentage to 0 (no TL rewards)
                    percentage.TlPercentage = tempOldFullPercentage * 0.75m;
                    percentage.RzwPercentage = tempOldFullPercentage * 0.25m;
                    percentage.ModifiedDate = DateTime.UtcNow;
                    // Update the record
                    context.PackageRewardPercentages.Update(percentage);
                }
            }

            // Save changes
            await context.SaveChangesAsync();

            //distribute the reward for the all deposits that are approved, create a new method for this task
            await DistributeRewardForApprovedDeposits(context, tokenPriceService, referralRewardService);
        }

    }

    private static async Task DistributeRewardForApprovedDeposits(AppDbContext context, ITokenPriceService tokenPriceService, ReferralRewardService referralRewardService)
    {
        var approvedDeposits = await context.Deposits.Where(p => p.Status == DepositStatus.Approved).ToListAsync();
        foreach (var deposit in approvedDeposits)
        {
            //call exisitng distribute reward method await _referralRewardService.ProcessDepositRewardsAsync(Id);
            await referralRewardService.ProcessDepositRewardsAsync(deposit.Id);
        }
    }



    private static async Task UndoRewarDistributionByPercentage(AppDbContext context, ITokenPriceService tokenPriceService)
    {

        // find all reward that are rewarded with percentage.Id
        var depositIds = context.ReferralRewards.Select(p => p.DepositId).Distinct().ToList();
        foreach (var depositId in depositIds)
        {
            var deposit = await context.Deposits.FirstOrDefaultAsync(p => p.Id == depositId);
            if (deposit == null) throw new Exception($"not found depositId: {depositId}");

            #region remove balance transactions
            var balanceTransactions = context.BalanceTransactions.Where(p => p.TransactionType == TransactionType.ReferralReward && p.ReferenceId == depositId && p.ReferenceType == BalanceTransactionReferenceTypes.Deposit);
            foreach (var balanceTransaction in balanceTransactions)
            {
                if (balanceTransaction.UserId == 11)
                {
                    Console.WriteLine("11");
                }
                var user = context.Users.FirstOrDefault(p => p.UserId == balanceTransaction.UserId);
                if (user == null) throw new Exception($"User not found userId: {balanceTransaction.UserId}");
                user.Balance -= balanceTransaction.Amount;
                await context.SaveChangesAsync();
                balanceTransaction.IsActive = false;
            }
            context.BalanceTransactions.RemoveRange(balanceTransactions);
            #endregion
            var rewardsOfDeposit = await context.ReferralRewards.Where(p => p.DepositId == depositId).OrderByDescending(p => p.Id).ToListAsync();
            foreach (var reward in rewardsOfDeposit)
            {
                if (reward.UserId == 11)
                {
                    Console.WriteLine("11");
                }
                #region undo trade record
                var tradeRecordQuery = context.Trades.Where(p => p.UserId == reward.UserId && (int)p.CoinAmount == (int)reward.RzwAmount && p.IsActive && p.Type == TradeType.ReferralReward).OrderByDescending(p => p.Id).AsQueryable();
                var tradeRecord = await tradeRecordQuery.FirstOrDefaultAsync();
                if (tradeRecord != null)
                {
                    tradeRecord.IsActive = false;
                    context.Trades.Update(tradeRecord);
                    //context.Trades.Remove(tradeRecord);
                }

                await context.SaveChangesAsync();
                #endregion

                #region undo rzw wallet balance adding

                // Get RZW token information (ID and price) in a single query
                var rzwTokenInfo = await tokenPriceService.GetRzwTokenInfoAsync();
                int rzwTokenId = rzwTokenInfo.TokenId;
                var userRzwWallet = context.Wallets.FirstOrDefault(p => p.UserId == reward.UserId && p.CoinId == rzwTokenId);
                if (userRzwWallet == null) throw new Exception($"User RZW wallet not found userId: {reward.UserId}");
                userRzwWallet.Balance -= reward.RzwAmount;
                reward.Status = ReferralRewardStatus.Cancelled;
                context.ReferralRewards.Update(reward);
                await context.SaveChangesAsync();
                #endregion
            }
            #region undo deposit status after revert reward distribution
            deposit.RewardStatus = DepositRewardStatus.Pending;
            deposit.ProcessStatus = null;
            await context.SaveChangesAsync();
            #endregion
        }


    }

    private static async Task TempUpdateReferenceCode(AppDbContext context, ReferralService referralService, SampleDataService _)
    {

        // <NAME_EMAIL> has a referral code
        var aliUser = await context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
        if (aliUser != null && string.IsNullOrEmpty(aliUser.ReferralCode))
        {
            aliUser.ReferralCode = await referralService.GenerateUniqueReferralCodeAsync();
            context.Users.Update(aliUser);
            await context.SaveChangesAsync();
        }

    }

    private static async Task TempCreateReferenceHierarchyForAliUser(AppDbContext context, ReferralService referralService, SampleDataService sampleDataService)
    {
        // <NAME_EMAIL> has a referral code
        var aliUser = await context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
        if (aliUser == null)
        {
            return;
        }
        if (string.IsNullOrEmpty(aliUser.ReferralCode))
        {
            aliUser.ReferralCode = await referralService.GenerateUniqueReferralCodeAsync();
            context.Users.Update(aliUser);
            await context.SaveChangesAsync();
        }
        //check if ali@veli has no referred users
        var aliReferredUsers = await context.Users
            .Where(u => u.ReferrerId == aliUser.UserId)
            .ToListAsync();
        if (aliReferredUsers.Count == 0)
        {
            // Create referral <NAME_EMAIL> (4 levels deep)
            await CreateReferralHierarchyAsync(context, referralService, aliUser, 1, 4);

            //// Generate sample data for all users if sampleDataService is provided
            //if (sampleDataService != null)
            //{
            //    // Get all users in the referral hierarchy
            //    var referralUsers = await context.Users
            //        .Where(u => u.UserId != aliUser.UserId && u.ReferrerId != null)
            //        .ToListAsync();

            //    // Generate sample data for each referred user
            //    foreach (var user in referralUsers)
            //    {
            //        await sampleDataService.GenerateSampleDataForUserAsync(user.UserId, true);
            //    }
            //}
        }
    }

    /// <summary>
    /// Initializes default RZW savings plans if none exist
    /// </summary>
    private static async Task InitializeRzwSavingsPlansAsync(AppDbContext context)
    {
        if (await context.RzwSavingsPlans.AnyAsync()) return;

        var now = DateTime.UtcNow;
        var plans = new List<RzwSavingsPlan>
        {
            new()
            {
                Name = "RZW 1 Gün Kilitli Tasarruf",
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                InterestRate = 0.000742m, // Günlük bileşik faiz (Yıllık %30 APY)
                MinRzwAmount = 100m,
                MaxRzwAmount = null, // Sınırsız
                IsActive = true,
                DisplayOrder = 1,
                Description = "1 Gün Kilitli Tasarruf. Yıllık yaklaşık %30 APY",
                CreatedDate = now
            },
            new()
            {
                Name = "RZW 7 Gün Kilitli Tasarruf",
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 7,
                InterestRate = 0.000959m, // Günlük bileşik faiz (Yıllık %40 APY)
                MinRzwAmount = 250m,
                MaxRzwAmount = null, // Sınırsız
                IsActive = true,
                DisplayOrder = 2,
                Description = "7 Gün Kilitli Tasarruf. Yıllık yaklaşık %40 APY",
                CreatedDate = now
            },
            new()
            {
                Name = "RZW 1 Ay Kilitli Tasarruf",
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 30,
                InterestRate = 0.001139m, // Günlük bileşik faiz (Yıllık %50 APY)
                MinRzwAmount = 500m,
                MaxRzwAmount = null, // Sınırsız
                IsActive = true,
                DisplayOrder = 3,
                Description = "1 Ay Kilitli Tasarruf. Yıllık yaklaşık %50 APY",
                CreatedDate = now
            },
            new()
            {
                Name = "RZW 3 Ay Kilitli Tasarruf",
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 90,
                InterestRate = 0.001294m, // Günlük bileşik faiz (Yıllık %60 APY)
                MinRzwAmount = 1000m,
                MaxRzwAmount = null, // Sınırsız
                IsActive = true,
                DisplayOrder = 4,
                Description = "3 Ay Kilitli Tasarruf. Yıllık yaklaşık %60 APY",
                CreatedDate = now
            },
            new()
            {
                Name = "RZW 6 Ay Kilitli Tasarruf",
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 180,
                InterestRate = 0.001433m, // Günlük bileşik faiz (Yıllık %70 APY)
                MinRzwAmount = 2500m,
                MaxRzwAmount = null, // Sınırsız
                IsActive = true,
                DisplayOrder = 5,
                Description = "6 Ay Kilitli Tasarruf. Yıllık yaklaşık %70 APY",
                CreatedDate = now
            },
            new()
            {
                Name = "RZW 1 Yıl Kilitli Tasarruf",
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 365,
                InterestRate = 0.001897m, // Günlük bileşik faiz (Yıllık %100 APY)
                MinRzwAmount = 5000m,
                MaxRzwAmount = null, // Sınırsız
                IsActive = true,
                DisplayOrder = 6,
                Description = "1 Yıl Kilitli Tasarruf. Yıllık yaklaşık %100 APY",
                CreatedDate = now
            }
        };

        context.RzwSavingsPlans.AddRange(plans);
        await context.SaveChangesAsync();
    }
}
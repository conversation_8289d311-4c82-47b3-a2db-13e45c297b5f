@model RazeWinComTr.ViewModels.EmailVerificationResultViewModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Model.Title;
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-body text-center p-5">
                    @if (Model.IsSuccess)
                    {
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        <h2 class="text-success mb-3">@Model.Title</h2>
                    }
                    else
                    {
                        <div class="mb-4">
                            <i class="fas fa-times-circle text-danger" style="font-size: 4rem;"></i>
                        </div>
                        <h2 class="text-danger mb-3">@Model.Title</h2>
                    }
                    
                    <p class="lead mb-4">@Model.Message</p>
                    
                    <div class="d-flex justify-content-center gap-3">
                        @if (Model.ShowLoginButton)
                        {
                            <a href="/Login" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>@Localizer["Login"]
                            </a>
                        }
                        
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>@Localizer["Home"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border: none;
        border-radius: 15px;
    }
    
    .card-body {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
    }
    
    .btn {
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 500;
    }
    
    .gap-3 {
        gap: 1rem !important;
    }
    
    .me-2 {
        margin-right: 0.5rem !important;
    }
</style>

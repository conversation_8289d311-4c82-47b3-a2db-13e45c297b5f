using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

/// <summary>
/// Generic verification service interface for all types of user verifications
/// </summary>
public interface IVerificationService
{
    /// <summary>
    /// Generates a new verification token for the user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <param name="targetValue">Target value to verify (email, phone, etc.)</param>
    /// <returns>Generated token</returns>
    Task<string> GenerateVerificationTokenAsync(int userId, VerificationType verificationType, string targetValue);

    /// <summary>
    /// Verifies the verification token
    /// </summary>
    /// <param name="token">Verification token</param>
    /// <returns>True if token is valid and verification was successful, false otherwise</returns>
    Task<bool> VerifyTokenAsync(string token);

    /// <summary>
    /// Checks if user can send verification (rate limiting)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>True if user can send verification, false if rate limited</returns>
    Task<bool> CanSendVerificationAsync(int userId, VerificationType verificationType);

    /// <summary>
    /// Gets the remaining time until user can send another verification
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>TimeSpan until next verification can be sent, null if can send now</returns>
    Task<TimeSpan?> GetTimeUntilNextVerificationAsync(int userId, VerificationType verificationType);

    /// <summary>
    /// Gets the number of verification attempts made today
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>Number of attempts today</returns>
    Task<int> GetTodayAttemptsAsync(int userId, VerificationType verificationType);

    /// <summary>
    /// Checks if user's verification is already completed
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>True if verification is completed, false otherwise</returns>
    Task<bool> IsVerifiedAsync(int userId, VerificationType verificationType);

    /// <summary>
    /// Gets user's verification status and related information
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>Verification status information</returns>
    Task<VerificationStatus> GetVerificationStatusAsync(int userId, VerificationType verificationType);
}

/// <summary>
/// Email-specific verification service interface
/// </summary>
public interface IEmailVerificationService
{
    /// <summary>
    /// Generates a new email verification token for the user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Generated token</returns>
    Task<string> GenerateVerificationTokenAsync(int userId);

    /// <summary>
    /// Sends email verification email to the user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="email">User email address</param>
    /// <param name="userName">User name for personalization</param>
    /// <returns>True if email was sent successfully, false otherwise</returns>
    Task<bool> SendVerificationEmailAsync(int userId, string email, string userName);

    /// <summary>
    /// Verifies the email verification token
    /// </summary>
    /// <param name="token">Verification token</param>
    /// <returns>True if token is valid and email was verified, false otherwise</returns>
    Task<bool> VerifyEmailTokenAsync(string token);

    /// <summary>
    /// Checks if user can send verification email (rate limiting)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>True if user can send email, false if rate limited</returns>
    Task<bool> CanSendVerificationEmailAsync(int userId);

    /// <summary>
    /// Gets the remaining time until user can send another verification email
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>TimeSpan until next email can be sent, null if can send now</returns>
    Task<TimeSpan?> GetTimeUntilNextEmailAsync(int userId);

    /// <summary>
    /// Gets the number of verification attempts made today
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Number of attempts today</returns>
    Task<int> GetTodayAttemptsAsync(int userId);

    /// <summary>
    /// Checks if user's email is already verified
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>True if email is verified, false otherwise</returns>
    Task<bool> IsEmailVerifiedAsync(int userId);

    /// <summary>
    /// Gets user's email verification status and related information
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Email verification status information</returns>
    Task<EmailVerificationStatus> GetVerificationStatusAsync(int userId);
}

/// <summary>
/// Generic verification status information
/// </summary>
public class VerificationStatus
{
    public bool IsVerified { get; set; }
    public DateTime? VerifiedDate { get; set; }
    public bool CanSendVerification { get; set; }
    public TimeSpan? TimeUntilNextVerification { get; set; }
    public int TodayAttempts { get; set; }
    public int MaxDailyAttempts { get; set; } = 5;
    public int MinutesBetweenAttempts { get; set; } = 5;
    public string TargetValue { get; set; } = string.Empty;
    public VerificationType VerificationType { get; set; }
}

/// <summary>
/// Email verification status information
/// </summary>
public class EmailVerificationStatus : VerificationStatus
{
    public EmailVerificationStatus()
    {
        VerificationType = VerificationType.Email;
    }
}

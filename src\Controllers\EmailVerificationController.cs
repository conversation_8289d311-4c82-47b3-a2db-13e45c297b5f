using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.ViewModels;

namespace RazeWinComTr.Controllers;

public class EmailVerificationController : Controller
{
    private readonly IEmailVerificationService _emailVerificationService;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<EmailVerificationController> _logger;

    public EmailVerificationController(
        IEmailVerificationService emailVerificationService,
        IStringLocalizer<SharedResource> localizer,
        ILogger<EmailVerificationController> logger)
    {
        _emailVerificationService = emailVerificationService;
        _localizer = localizer;
        _logger = logger;
    }

    /// <summary>
    /// Handles email verification when user clicks the verification link
    /// </summary>
    /// <param name="token">Verification token from email</param>
    /// <returns>Verification result page</returns>
    [HttpGet]
    public async Task<IActionResult> Verify(string token)
    {
        if (string.IsNullOrEmpty(token))
        {
            _logger.LogWarning("Email verification attempted with empty token");
            return View("VerificationResult", new EmailVerificationResultViewModel
            {
                IsSuccess = false,
                Title = _localizer["Verification Failed"],
                Message = _localizer["Invalid verification link. Please check your email and try again."],
                ShowLoginButton = false
            });
        }

        try
        {
            var isVerified = await _emailVerificationService.VerifyEmailTokenAsync(token);

            if (isVerified)
            {
                _logger.LogInformation("Email verification successful for token: {Token}", token);
                return View("VerificationResult", new EmailVerificationResultViewModel
                {
                    IsSuccess = true,
                    Title = _localizer["Email Verified Successfully"],
                    Message = _localizer["Your email address has been verified successfully. You can now log in to your account."],
                    ShowLoginButton = true
                });
            }
            else
            {
                _logger.LogWarning("Email verification failed for token: {Token}", token);
                return View("VerificationResult", new EmailVerificationResultViewModel
                {
                    IsSuccess = false,
                    Title = _localizer["Verification Failed"],
                    Message = _localizer["The verification link is invalid or has expired. Please request a new verification email from your profile page."],
                    ShowLoginButton = true
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during email verification for token: {Token}", token);
            return View("VerificationResult", new EmailVerificationResultViewModel
            {
                IsSuccess = false,
                Title = _localizer["Verification Error"],
                Message = _localizer["An error occurred while verifying your email. Please try again later or contact support."],
                ShowLoginButton = false
            });
        }
    }

    /// <summary>
    /// AJAX endpoint for sending verification email from profile page
    /// </summary>
    /// <returns>JSON result indicating success or failure</returns>
    [HttpPost]
    [Route("SendVerificationEmail")]
    [Authorize(AuthenticationSchemes = "UserCookieScheme")]
    public async Task<IActionResult> SendVerificationEmail()
    {
        try
        {
            // Get current user ID from claims
            var userIdClaim = User.FindFirst("UserId");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Json(new { success = false, message = _localizer["User not found. Please log in again."].Value });
            }

            // Get user email from claims
            var emailClaim = User.FindFirst("Email");
            if (emailClaim == null)
            {
                return Json(new { success = false, message = _localizer["User email not found. Please log in again."].Value });
            }

            // Get user name from claims
            var nameClaim = User.FindFirst("Name");
            var userName = nameClaim?.Value ?? "User";

            // Check if user can send verification email (rate limiting)
            var canSend = await _emailVerificationService.CanSendVerificationEmailAsync(userId);
            if (!canSend)
            {
                var timeUntilNext = await _emailVerificationService.GetTimeUntilNextEmailAsync(userId);
                if (timeUntilNext.HasValue)
                {
                    var minutes = (int)Math.Ceiling(timeUntilNext.Value.TotalMinutes);
                    return Json(new { 
                        success = false, 
                        message = _localizer["You can send another verification email in {0} minutes.", minutes].Value 
                    });
                }
                else
                {
                    return Json(new { 
                        success = false, 
                        message = _localizer["You have reached the daily limit for verification emails. Please try again tomorrow."].Value 
                    });
                }
            }

            // Check if email is already verified
            var isVerified = await _emailVerificationService.IsEmailVerifiedAsync(userId);
            if (isVerified)
            {
                return Json(new { success = false, message = _localizer["Your email is already verified."].Value });
            }

            // Send verification email
            var emailSent = await _emailVerificationService.SendVerificationEmailAsync(userId, emailClaim.Value, userName);

            if (emailSent)
            {
                return Json(new { 
                    success = true, 
                    message = _localizer["Verification email sent successfully. Please check your inbox."].Value 
                });
            }
            else
            {
                return Json(new { 
                    success = false, 
                    message = _localizer["Failed to send verification email. Please try again later."].Value 
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending verification email");
            return Json(new { 
                success = false, 
                message = _localizer["An error occurred while sending the verification email. Please try again later."].Value 
            });
        }
    }
}



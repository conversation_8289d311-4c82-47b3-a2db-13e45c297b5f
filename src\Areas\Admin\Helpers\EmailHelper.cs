using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Logging;
using MimeKit;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Areas.Admin.Helpers
{
    public class EmailHelper : IEmailHelper
    {
        private readonly SettingService _settingService;
        private readonly ILogger<EmailHelper> _logger;
        private readonly IWebHostEnvironment _environment;

        public EmailHelper(SettingService settingService, ILogger<EmailHelper> logger, IWebHostEnvironment environment)
        {
            _settingService = settingService;
            _logger = logger;
            _environment = environment;
        }

        /// <summary>
        /// Gets the SMTP settings from the database
        /// </summary>
        /// <returns>SMTP settings</returns>
        public async Task<SmtpSettings> GetSmtpSettingsAsync()
        {
            var settings = new SmtpSettings
            {
                Host = await _settingService.GetSettingAsync("smtp_host", ""),
                Port = await _settingService.GetIntSettingAsync("smtp_port", 587),
                Username = await _settingService.GetSettingAsync("smtp_user", ""),
                Password = await _settingService.GetSettingAsync("smtp_pass", ""),
                Security = await _settingService.GetSettingAsync("smtp_crypto", "tls")
            };

            // Set sender info based on environment
            if (_environment.IsProduction())
            {
                settings.SenderEmail = await _settingService.GetSettingAsync("site_email", "<EMAIL>");
                settings.SenderName = await _settingService.GetSettingAsync("site_title", "RAZEWIN");
            }
            else
            {
                // Non-production: Use SMTP user as sender and ensure it's not razewin.com.tr
                var senderEmail = await _settingService.GetSettingAsync("smtp_user", "");

                // Production domain protection: Don't use razewin.com.tr in non-production
                if (senderEmail.Contains("razewin.com.tr", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogWarning("Production email domain detected in non-production environment. Using fallback.");
                    senderEmail = "<EMAIL>"; // Fallback to test email
                }

                settings.SenderEmail = senderEmail;
                settings.SenderName = $"[{_environment.EnvironmentName.ToUpper()}] Test Platform";
            }

            return settings;
        }

        /// <summary>
        /// Sends an email message
        /// </summary>
        /// <param name="message">The email message to send</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        public async Task<bool> SendEmailAsync(EmailMessage message)
        {
            try
            {
                var settings = await GetSmtpSettingsAsync();

                // Validate settings
                if (string.IsNullOrEmpty(settings.Host) || 
                    string.IsNullOrEmpty(settings.Username) || 
                    string.IsNullOrEmpty(settings.Password))
                {
                    _logger.LogError("SMTP settings are not configured properly");
                    return false;
                }

                // Create the email message
                var email = new MimeMessage();

                // Set the sender
                email.From.Add(new MailboxAddress(settings.SenderName, settings.SenderEmail));

                // Add recipients
                foreach (var to in message.ToAddresses)
                {
                    email.To.Add(new MailboxAddress(to.Name, to.Address));
                }

                foreach (var cc in message.CcAddresses)
                {
                    email.Cc.Add(new MailboxAddress(cc.Name, cc.Address));
                }

                foreach (var bcc in message.BccAddresses)
                {
                    email.Bcc.Add(new MailboxAddress(bcc.Name, bcc.Address));
                }

                // Set subject
                email.Subject = message.Subject;

                // Create the body
                var builder = new BodyBuilder();
                if (message.IsHtml)
                {
                    builder.HtmlBody = message.Body;
                }
                else
                {
                    builder.TextBody = message.Body;
                }

                // Add attachments
                foreach (var attachment in message.Attachments)
                {
                    builder.Attachments.Add(attachment.FileName, attachment.Content, ContentType.Parse(attachment.ContentType));
                }

                email.Body = builder.ToMessageBody();

                // Send the email
                using (var client = new SmtpClient())
                {
                    // Configure security options
                    SecureSocketOptions securityOptions = SecureSocketOptions.Auto;
                    if (settings.Security.ToLower() == "ssl")
                    {
                        securityOptions = SecureSocketOptions.SslOnConnect;
                    }
                    else if (settings.Security.ToLower() == "tls")
                    {
                        securityOptions = SecureSocketOptions.StartTls;
                    }

                    // Connect to the SMTP server
                    await client.ConnectAsync(settings.Host, settings.Port, securityOptions);

                    // Authenticate
                    await client.AuthenticateAsync(settings.Username, settings.Password);

                    // Send the email
                    await client.SendAsync(email);

                    // Disconnect
                    await client.DisconnectAsync(true);
                }

                _logger.LogInformation("Email sent successfully to {Recipients}", string.Join(", ", message.ToAddresses.Select(a => a.Address)));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email: {ErrorMessage}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Sends a simple email message to a single recipient
        /// </summary>
        /// <param name="to">Recipient email address</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body</param>
        /// <param name="isHtml">Whether the body is HTML</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        public async Task<bool> SendSimpleEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            var message = new EmailMessage
            {
                Subject = subject,
                Body = body,
                IsHtml = isHtml,
                ToAddresses = new List<EmailAddress> { new EmailAddress(to) }
            };

            return await SendEmailAsync(message);
        }

        /// <summary>
        /// Sends a simple email message to multiple recipients
        /// </summary>
        /// <param name="to">List of recipient email addresses</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body</param>
        /// <param name="isHtml">Whether the body is HTML</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        public async Task<bool> SendSimpleEmailAsync(List<string> to, string subject, string body, bool isHtml = true)
        {
            var message = new EmailMessage
            {
                Subject = subject,
                Body = body,
                IsHtml = isHtml,
                ToAddresses = to.Select(email => new EmailAddress(email)).ToList()
            };

            return await SendEmailAsync(message);
        }
    }
}
